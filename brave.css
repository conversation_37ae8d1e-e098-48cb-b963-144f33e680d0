body {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-size: 12px;
    font-weight: normal;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: none;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Estilos para a interface do gota.io */
.gota-interface {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: white;
}

.gota-container {
    display: flex;
    width: 900px;
    height: 600px;
    background: rgba(42, 42, 42, 0.95);
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.gota-left-panel {
    width: 350px;
    padding: 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.gota-right-panel {
    flex: 1;
    background: rgba(0, 0, 0, 0.3);
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.gota-input {
    width: 100%;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-size: 16px;
    outline: none;
    transition: all 0.3s ease;
}

.gota-input:focus {
    border-color: rgba(255, 255, 255, 0.5);
}

.gota-button {
    width: 100%;
    padding: 15px;
    background: rgba(64, 64, 64, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gota-button:hover {
    background: rgba(80, 80, 80, 0.9);
}

.gota-play-button {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.gota-play-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.gota-spectate-button {
    width: 100%;
    padding: 18px;
    background: linear-gradient(135deg, #9C27B0, #8E24AA);
    border: none;
    border-radius: 10px;
    color: white;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

.gota-spectate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(156, 39, 176, 0.4);
}

.gota-server-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(255, 255, 255, 0.1);
}

.gota-server-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.gota-server-list {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.gota-server-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.gota-server-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

.gota-server-name {
    font-weight: 500;
    font-size: 16px;
}

.gota-server-mode {
    font-size: 14px;
    opacity: 0.8;
    color: #ccc;
}

@font-face {
    font-family: "Open Sans";
    src: url("fonts/OpenSans-Regular.ttf");
}

@font-face {
    font-family: "IceCaps";
    src: url("fonts/IceCaps.ttf");
}

@font-face {
    font-family: "BrazierFlame";
    src: url("fonts/BrazierFlame.ttf");
}

@font-face {
    font-family: "burnstown";
    src: url("fonts/burnstown.ttf");
}

@font-face {
    font-family: "chlorinar";
    src: url("fonts/chlorinar.ttf");
}

@font-face {
    font-family: "Facon";
    src: url("fonts/Facon.ttf");
}

@font-face {
    font-family: "ampad";
    src: url("fonts/ampad.ttf");
}

@font-face {
    font-family: "archistico";
    src: url("fonts/archistico.ttf");
}

@font-face {
    font-family: "breakaway";
    src: url("fonts/breakaway.ttf");
}

@font-face {
    font-family: "conformity";
    src: url("fonts/conformity.ttf");
}

@font-face {
    font-family: "electroharmonix";
    src: url("fonts/electroharmonix.ttf");
}

@font-face {
    font-family: "PWJoyeuxNoel";
    src: url("fonts/PWJoyeuxNoel.ttf");
}

@font-face {
    font-family: "leckerli-one";
    src: url("fonts/leckerli-one.ttf");
}

.t1 {
    color: #25f;
}

.t2 {
    color: #f31;
}

.t3 {
    color: #11BB11;
}

.t4 {
    color: #DDDD11;
}

.t5 {
    color: #F011F0;
}

.t6 {
    color: #FFB20F;
}

.t7 {
    color: #FF9DDB;
}

.t8 {
    color: #915C00;
}

.center {
    text-align: center;
}

span {
    pointer-events: none;
}

.red {
    text-align: center;
    color: #ff3311;
}

.ui-pane {
    position: absolute;
    display: inline-block;
    background-color: rgba(0, 0, 0, .7);
    border: solid 2px rgba(255, 255, 255, .2);
    border-radius: 5px;
}

.hud-panel {
    pointer-events: none;
}

.ui-scale {
    transform: scale(1);
}

.top-left {
    position: absolute;
    top: 10px;
    left: 10px;
    transform-origin: top left;
}

.top-right {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 200px;
    transform-origin: top right;
}

#canvas-container {
    width: 100%;
    height: 100%;
}

#canvas {
    position: absolute;
    width: 100%;
    height: 100%;
}

#canvas-background {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: -1;
}

#score-panel {
    position: relative;
    display: block;
    color: white;
    width: auto;
    /* max-width: 300px; */
    height: auto;
    font-family: Calibri;
    font-weight: 700;
    font-size: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
}

#score-panel p {
    margin: 0px 10px 0px 10px;
    white-space: nowrap
}

#score-mouse {
    color: red;
    display: none;
}

#extra-reset-timer {
    display: none;
}

#party-panel {
    display: inline-block;
    position: relative;
    color: white;
    top: 10px;
}

#party-canvas {
    pointer-events: auto;
}

#party-header {
    font-family: Calibri;
    font-weight: 700;
    font-size: 16px;
    text-align: center;
    margin: 3px 0 -5px 0;
}

#extra-panel {
    position: relative;
    display: inline-block;
    color: white;
    width: 200px;
    font-family: Calibri;
    font-weight: 700;
    font-size: 16px;
    padding-top: 5px;
    padding-bottom: 5px;
    margin-top: 10px;
    height: auto;
}

#extra-panel p {
    margin: 0px 10px 0px 10px;
    white-space: nowrap
}

#leaderboard-panel {
    position: relative;
    display: inline-block;
    color: white;
    width: 200px;
    font-family: Calibri;
    font-weight: 700;
    font-size: 16pt;
}

.lh {
    top: 0px;
    text-align: center;
    margin: 5px 0px 0px 0px;
}

#leaderboard-canvas {
    margin-bottom: 5px;
    margin-left: auto;
    margin-right: auto;
}

#chat-panel {
    background-color: transparent;
    border-radius: 0px;
    border: none;
    color: white;
    bottom: 15px;
    left: 10px;
    border: 2px solid rgba(255, 255, 255, .2);
    border-radius: 5px;
    width: 400px;
    height: 250px;
    min-width: 200px;
    min-height: 125px;
    max-width: 800px;
    max-height: 500px;
    overflow: hidden;
    transform-origin: bottom left;
    font-family: "Open Sans";
}

.chat-name {
    margin-right: 2px;
    pointer-events: all;
    position: relative;
    display: inline-block;
    z-index: 1;
}

#autocomplete-panel {
    position: absolute;
    display: none;
    width: 100%;
    height: 42%;
    left: 0;
    bottom: 24px;
    border: none;
    border-top: 2px solid rgba(255, 255, 255, .2);
    border-radius: 0;
    overflow-x: hidden;
    overflow-y: scroll;
    font-family: "Open Sans";
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

#autocomplete-panel::-webkit-scrollbar {
    width: 6px;
}

#autocomplete-panel::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

#autocomplete-panel table {
    width: 100%;
    border-collapse: collapse;
    margin-left: auto;
    margin-right: auto;
}

#autocomplete-panel table tbody tr {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    height: 20px;
}

#autocomplete-panel table tbody tr:hover {
    border-bottom: 1px solid rgba(255, 255, 255, .7);
}

#autocomplete-panel table tr th:nth-child(1),
#autocomplete-panel table tr td:nth-child(1) {
    width: 20%;
    text-align: left;
}

#autocomplete-panel table tr th:nth-child(2),
#autocomplete-panel table tr td:nth-child(2) {
    width: 20%;
    text-align: center;
}

#autocomplete-panel table tr th:nth-child(3),
#autocomplete-panel table tr td:nth-child(3) {
    width: 60%;
    text-align: right;
}

#autocomplete-panel h3 {
    margin: 0;
    padding: 3px;
}

#autocomplete-panel img {
    width: 25px;
    height: 25px;
    padding: 5px;
    margin: 0 0 4px 4px;
    border-radius: 4px;
}

#autocomplete-panel img:hover {
    box-shadow: inset 0px 0px 5px #000;
}

#emote-panel {
    display: none;
    position: absolute;
    bottom: 22px;
    right: 10px;
    width: 43%;
    height: 48%;
    border-radius: 5px 5px 0 0;
    border-bottom: 0px;
    overflow-x: hidden;
    overflow-y: scroll;
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

#emote-panel::-webkit-scrollbar {
    width: 6px;
}

#emote-panel::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

#emote-panel h3 {
    margin: 0;
    padding: 2px 0 0 5px;
    font-family: Calibri;
    font-weight: 900;
}

#emote-panel ul {
    display: flex;
    flex-flow: row wrap;
    list-style-type: none;
    padding: 0;
    margin: 2px 0 0 3px;
}

#emote-panel ul li {
    padding: 4px;
    width: 25px;
    height: 25px;
    border: 1px solid rgba(0, 0, 0, 0);
    border-radius: 4px;
}

#emote-panel ul li:hover {
    border: 1px solid #000;
    box-shadow: inset 0px 0px 5px #000;
}

#emote-panel ul li img {
    width: 25px;
    height: 25px;
}

#minimap-panel {
    bottom: 10px;
    right: 10px;
    height: 250px;
    width: 250px;
    overflow: hidden;
    transform-origin: bottom right;
}

#minimap-coordinates {
    display: none;
    font-family: Calibri;
    font-weight: 300;
    font-size: 16px;
    color: white;
    height: 20px;
    margin: auto;
    text-align: center;
}

.menu-sub-bg {
    background-color: #363636;
}

.menu-sub-bg2 {
    background-color: #404040;
}

.main {
    position: absolute;
    margin: 0 auto;
    top: 6%;
    right: 0;
    bottom: 0;
    left: 0;
    width: 1020px;
    /* 3-panel: 1000px : 2-panel: 658px*/
    height: 470px;
    z-index: 2;
    padding: 10px;
    font-family: "Open Sans";
    color: #fff;
}

.main-panel-wrapper {
    display: flex;
    justify-content: space-between;
}

.main-divider {
    border-radius: 5px;
    display: inline-block;
    vertical-align: top;
    margin-right: 10px;
}

.main-panel {
    background-color: #2A2A2A;
    border-radius: 10px;
}

.main-left {
    width: 370px;
    height: 470px;
}

.main-rb-title {
    display: block;
    text-align: center;
    font-size: 14px;
    height: 20px;
    margin: 0;
    padding: 0;
    color: inherit;
}

#main-rb {
    width: 350px;
    height: 300px;
    margin-bottom: 10px;
    text-align: center;
}

#inner-rb {
    background-color: #000000;
    position: relative;
}

#main-account {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 350px;
    height: 160px;
    font-family: "Open Sans";
}

.avatar {
    display: flex;
    align-self: center;
    border-radius: 50%;
    width: 86px !important;
    height: 86px !important;
}

#authed {
    display: flex;
}

#guest {
    text-align: center;
    color: #FFF;
}

#userinfo {
    display: flex;
    padding: 10px 0;
    width: 230px;
    flex-direction: column;
    text-align: center;
}

#username-container {
    display: flex;
    flex-direction: column;
    color: inherit;
    text-align: center;
}

#account-username {
    font-size: 18px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

#account-titles {
    width: 242px;
    height: 30px;
    font-size: 14pt;
    border-radius: 10px;
}

#account-actions {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-self: center;
    width: 100px;
    padding: 0 10px;
    height: 100%;
}

#account-actions .gota-btn {
    height: 30px;
}

.xp-meter {
    height: 8px;
    width: 200px;
    margin: 5px;
    align-self: center;
    position: relative;
    background: #555;
}

.xp-meter>span {
    display: block;
    height: 100%;
    background-color: rgb(43, 194, 83);
    position: relative;
    overflow: hidden;
}

.main-content {
    width: 330px;
    height: 470px;
}

#main-side {
    width: 320px;
    height: 470px;
    /* UI: 490px default, 300px condensed */
    /* margin-bottom: 10px; */
}

#main-right {
    width: 340px;
    height: 470px;
}

.main-mid {
    padding: 10px 0 5px;
}

.main-input-btns {
    width: 91%;
    margin: 6px auto 0;
}

.main-bottom {
    display: flex;
    /* text-align: center; */
    width: 100%;
    height: 190px;
    margin-top: 10px;
}

.main-bottom-left {
    width: 50%;
    height: 190px;
    margin-right: 10px;
}

.main-bottom-right {
    width: 50%;
    height: 100%;
    text-align: center;
}

.main-bottom-stats {
    /* border: solid 1px white; */
    /* box-shadow: 0px 0px 3px 3px rgb(0 0 0 / 52%); */
    margin: auto 0 auto auto;
    width: 90%;
    height: 96%;
    border-radius: 3px 10px 10px 3px;
    font-size: 18px;
    font-weight: 700;
    float: left;
}

.main-bottom-links {
    display: flex;
    justify-content: space-around;
    width: 90%;
    height: 28px;
    margin: 5px auto;
    padding: 0;
}

.main-bottom-links a {
    text-decoration: none;
    color: #fff;
    font-size: 24px;
}

#main-stats {
    font-family: "Open Sans";
    font-size: 8pt;
    text-align: left;
    margin-left: 5px;
    overflow: auto;
    pointer-events: auto;
}

#logo {
    width: 110%;
    height: 120px;
    background-image: url("images/logo.png");
    background-position: -15px 0px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: -15px;
    top: -20px;
    position: relative;
}

.main-version {
    display: block;
    color: inherit;
    font-size: 10px;
    text-align: center;
    height: 15px;
}

#name-box {
    display: block;
    height: 28px;
    font-size: 15pt;
    width: 90%;
    margin: 0 auto;
    border-width: 0px;
    border-radius: 5px;
    text-indent: 5px;
    text-align: center;
    cursor: pointer;
    font-family: "Open Sans";
}

#btn-play {
    background-color: #f1b73f;
    margin-right: 6px;
}

#btn-spec {
    background-color: #d670c5;
}

.gota-menu-btn {
    width: 138px;
    height: 35px;
    outline: none;
    border-width: 0px;
    border-radius: 5px;
    color: white;
    font-size: 14pt;
    cursor: pointer;
    font-family: "Open Sans";
    box-shadow: 1px 1px rgba(0, 0, 0, 0.25);
    transition: all 0.5s;
}

.gota-menu-btn:hover {
    box-shadow: inset 0 0 0 10em rgba(255, 255, 255, 0.2);
}

input:focus {
    outline: none;
    border-color: rgba(94, 174, 255, .7);
}

.gota-btn {
    background-color: #444444;
    border-width: 0px;
    margin-bottom: 0px;
    border-radius: 5px;
    height: 30px;
    min-width: 80px;
    color: white;
    font-family: "Open Sans";
    cursor: pointer;
    transition: all 0.5s;
}

.gota-btn:hover {
    box-shadow: inset 0 0 0 10em rgba(255, 255, 255, 0.2);
}

.gota-btn:focus {
    outline: none;
}

.gota-input {
    border-radius: 7px;
    font-size: 16px;
    text-indent: 5px;
    text-align: center;
}

.gota-input-group {
    color: white;
    width: 90%;
    margin: 10px auto;
    text-align: center;
}

.gota-input-group input[type="text"] {
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
    color: #000;
    font-size: 16px;
    height: 20px;
    width: 150px;
    text-indent: 5px;
}

.bottom-btn {
    display: inline-block;
    width: 140px;
    height: 17%;
    /* default 30 px */
    margin-bottom: 5px;
    float: right;
    border-radius: 15px 3px 3px 15px !important;
}

.divider {
    width: 280px;
    height: 5px;
    margin: 0px auto 7px;
    background-image: url("images/divider.png");
    background-size: 100%;
    background-repeat: no-repeat;
}

.top-divider {
    margin-top: 24px;
    background-image: none;
}

.main-right-panel {
    color: inherit;
    /* width: 100%;
    height: 100%; */
}

.title-text {
    font-size: 16pt;
    text-align: center;
    font-family: "Open Sans";
}

.menu-title {
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10px 10px 0 0;
    background-color: #404040;
}

.title-divider {
    margin-top: 5px;
    margin-bottom: 5px;
}

.server-container {
    width: 100%;
    margin-top: 10px;
    margin-bottom: 0;
    font-size: 14px;
    margin-left: auto;
    margin-right: auto;
}

#server-tab-container {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: space-between;
}

.server-tab {
    width: 120px;
    text-align: center;
}

.server-tab:first-child {
    width: 100px;
    border-top-left-radius: 5px;
}

.server-tab:last-child {
    width: 100px;
    border-top-right-radius: 5px;
}

.server-tab:hover {
    cursor: pointer;
}

.server-tab span {
    display: block;
    padding: 2px 0px;
    font-size: 14px;
    text-shadow: 2px 2px 3px #000000;
    text-decoration: none;
}

#server-content {
    display: block;
    float: left;
    background-color: rgba(50, 50, 50, 0.8);
    width: 100%;
    height: 400px;
    padding: 0;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.server-table {
    table-layout: fixed;
    width: 97%;
    margin: 0 auto;
}

.server-table-name {
    width: 105px;
    white-space: nowrap;
    text-align: left;
}

.server-table-players {
    width: 100px;
    white-space: nowrap;
    text-align: center;
}

.server-table-mode {
    width: 150px;
    white-space: nowrap;
    text-align: right;
}

.server-table thead {
    display: block;
}

.server-table tbody {
    display: block;
    overflow: auto;
    width: 100%;
    height: 365px;
    background-color: rgba(255, 255, 255, 0.25);
    border-radius: 4px;
    border-collapse: collapse;
}

.server-table tbody::-webkit-scrollbar {
    width: 7px;
}

.server-table tbody::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

.server-row {
    cursor: pointer;
    height: 20px;
}

.account-server {
    background-color: rgba(246, 115, 115, .7);
}

.server-selected {
    background-color: rgba(94, 174, 255, .7);
}

#chat-tab-container {
    list-style-type: none;
    margin: 0;
    padding: 0;
    display: flex;
    font-size: 14pt;
    height: 30px;
    border-bottom: 2px solid rgba(255, 255, 255, .2);
    pointer-events: auto;
}

.chat-tab {
    text-align: center;
    padding: 0 10px;
    border-right: 2px solid rgba(255, 255, 255, .2);
    opacity: 0.6;
}

.chat-tab:hover {
    cursor: pointer;
}

.chat-tab span {
    display: block;
    padding: 2px 0px;
    text-shadow: 2px 2px 3px #000000;
    text-decoration: none;
}

.chat-active-tab {
    font-weight: 700;
    opacity: 1.0;
}

#chat-components {
    display: flex;
    border-top: 2px solid rgba(255, 255, 255, .2);
}

#chat-container {
    display: block;
    padding: 0;
    margin: 0;
    width: 100%;
    height: calc(100% - 58px);
    border-color: rgba(255, 255, 255, .2);
    word-break: break-word;
    background-color: rgba(0, 0, 0, .7);
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

.chat-inner-container {
    height: 100%;
    width: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.chat-inner-container::-webkit-scrollbar {
    width: 7px;
}

.chat-inner-container::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

#chat-resize {
    position: absolute;
    display: block;
    width: 10px;
    height: 10px;
    right: 0;
    top: 0;
    pointer-events: auto;
    cursor: ne-resize;
    display: none;
}

#chat-input {
    background-color: rgba(255, 255, 255, .6);
    border: none;
    border-right: 1px solid rgb(71, 71, 71);
    font-size: 14px;
    width: calc(100% - 25px);
    height: 22px;
    text-indent: 5px;
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

#chat-emote-btn {
    background-color: rgba(255, 255, 255, .6);
    background-position: center;
    background-size: 20px 20px;
    background-repeat: no-repeat;
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    padding: 0;
    margin: 0;
    width: 24px;
    height: 24px;
    border: none;
    border-left: 1px solid rgb(71, 71, 71);
    pointer-events: auto;
    user-select: auto;
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
}

#chat-emote-btn:hover {
    -webkit-filter: none;
    filter: none;
    box-shadow: inset 0px 0px 5px #000;
}

#chat-emote-btn:focus {
    outline: 0;
}

#chat-table {
    table-layout: fixed;
}

.chat-text {
    word-wrap: break-word;
    pointer-events: none;
    user-select: auto;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    position: relative;
    display: inline-block;
    z-index: 1;
}

.chat-table td {
    display: table-cell;
    vertical-align: top;
    font-size: 14px;
}

.chat-table span {
    vertical-align: top;
}

.options-container {
    display: block;
    margin-top: 10px;
    margin-bottom: 0;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    height: 420px;
    background-color: rgba(50, 50, 50, 0.8);
    font-size: 14px;
    padding: 0 10px 10px;
    box-sizing: border-box;
    border-radius: 10px;
    overflow-x: hidden;
    overflow-y: auto;
    font-family: "Open Sans";
}

.options-container::-webkit-scrollbar {
    width: 6px;
}

.options-container::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

.options-container input[type=text] {
    border-radius: 5px;
}

.options-container input {
    float: right;
}

.options-container select {
    float: right;
    width: 100px;
    /* height: 30px; */
}

.options-input {
    width: 20%;
    text-align: center;
    border-radius: 5px;
    background: rgba(128, 128, 128, 128);
    border: none;
    outline: none;
    color: white;
}

.keybinds-btn-selected {
    background-color: rgba(255, 40, 40, .9);
}

#btn-reset-keybinds {
    display: block;
    margin: 20px auto 0px;
    width: 125px;
}

#btn-theme-export,
#btn-theme-import {
    width: 100%;
}

#subpanel-content select {
    width: 128px;
}

#subpanel-rules .title-text {
    display: block;
}

#btn-subpanel-rules {
    display: block;
    margin: 0 auto 10px;
}

.locked-name-rules {
    display: block;
    padding-top: 0;
    padding-left: 20px;
}

.options-table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}

.options-table a {
    color: inherit;
}

.options-table a:hover {
    color: currentColor;
    filter: brightness(150%);
}

.options-table .keybinds-btn {
    border-radius: 5px;
    width: 65px;
    height: 20px;
    border: 1px solid rgb(169, 169, 169);
}

.options-table button:focus {
    outline: none;
}

.options-table a {
    text-decoration: underline;
}

.options-table #spSkinName {
    width: 120px;
}

/* .options-table select {
    border-radius: 5px;
    width: 65px;
    height: 20px;
    border: 1px solid rgb(169, 169, 169);
} */

.options-table select:focus {
    outline: none;
}

.options-table th {
    font-size: 16px;
}

.options-table tbody tr td:nth-child(2n + 2) {
    text-align: right;
}

.options-table thead:not(:first-child) tr th {
    padding-top: 10px;
}

.options-container select:focus {
    outline: none;
}

.popup-container {
    position: absolute;
    display: inline-block;
    margin: auto;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    width: 320px;
    height: 400px;
    z-index: 1;
    pointer-events: none;
}

.popup-panel {
    background-color: rgba(0, 0, 0, .4);
    pointer-events: auto;
    border-radius: 10px;
    /*
    border-color: white;
    border-style: solid;
    border-width: 2px;
    border-color: #ffffff;
    */
}

#popup-party {
    width: 320px;
    overflow: hidden;
}

#popup-party-text {
    width: 90%;
    margin: 10px auto;
    text-align: center;
    font-family: "Open Sans";
    font-size: 12pt;
}

#popup-party-join {
    width: 320px;
    overflow: hidden;
}

.popup-buttons {
    height: 30px;
    text-align: center;
    margin-top: 20px;
    margin-bottom: 15px;
    margin-left: auto;
    margin-right: auto;
}

#btn-accept {
    margin-right: 30px;
}

#popup-login {
    width: 320px;
    margin-bottom: 10px;
}

#popup-profile {
    width: 320px;
    font-family: "Open Sans";
    padding: 10px 0;
}

.popup-profile-filler {
    height: 80px;
}

.profile-title-container {
    display: flex;
    flex-direction: column;
    padding: 10px;
}

#profile-title {
    text-shadow: none;
    font-weight: 700;
    text-decoration: underline;
    pointer-events: all;
}

#profile-username {
    pointer-events: all;
}

#profile-level {
    font-size: 14px;
    text-align: center;
}

#profile-clan-membership {
    font-size: 16px;
    text-align: center;
    font-weight: bold;
}

.profle-inline-title {
    display: flex;
    justify-content: center;
    font-size: 20px;
    align-items: center;
}

#profile-ranked-region {
    border-radius: 5px;
}

.profile-ranked-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.profile-ranked-container div {
    display: flex;
    flex-direction: column;
    padding: 20px 0;
    text-align: center;
    width: 33%;
}

.ranked-info-unavailable {
    display: block;
    text-align: center;
    color: red;
    padding: 15px 5px;
}

.profile-actions {
    display: flex;
    justify-content: center;
}

#main-social {
    color: white;
    width: 840px;
}

.user-list {
    list-style: none;
    font-size: 13px;
    padding-inline-start: 10px;
}

.user-list img {
    margin: 5px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.user-list>li {
    display: flex;
    margin: 0 0 10px 5px;
    user-select: none;
    width: 95%;
    border-radius: 10px;
}

.user-list .info {
    flex: 1;
}

.user-list .status {
    position: relative;
    margin-left: 14px;
    color: #a8adb3;
}

.offline:after,
.online:after,
.spectate:after,
.pending:after {
    content: '';
    left: -12px;
    top: 6px;
    position: absolute;
    height: 7px;
    width: 7px;
    border-radius: 50%;
}

.offline:after {
    background: #757575;
}

.online:after {
    background: #62bf63;
}

.spectate:after {
    background: #fd8064;
}

.pending:after {
    background: #df78ef
}

.user-embed .info {
    display: inline-flex;
    flex-direction: column;
    width: 50%;
    height: 60px;
    margin: auto 5px;
    overflow-y: hidden;
    justify-content: center;
}

.user-embed .info .username {
    font-weight: 600;
    color: #fff;
    height: 30%;
    cursor: pointer;
}

.user-embed .actions {
    margin: auto 0;
    height: 38px;
    visibility: hidden;
}

.user-embed .actions .material-icons {
    font-size: 40px !important;
    margin-right: 5px;
}

.user-embed:hover .actions {
    visibility: visible;
}

#social-actions {
    display: flex;
    flex-direction: column;
    text-align: center;
}

#social-actions button {
    margin: 5px 20% 5px 20%;
}

#social-uid {
    pointer-events: all !important;
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
}

#social-online-friends-count {
    color: #00ff00;
}

#main-friends {
    width: 480px;
}

#social-friends-info {
    height: 20px;
    width: 100%;
    margin-top: 5px;
    text-align: center;
}

#social-friends-info>span {
    display: block;
}

#social-friends {
    height: calc(90% - 40px - 25px - 40px);
    width: calc(100% - 40px);
    margin: 20px auto 5px;
    overflow-y: auto;
    overflow-x: hidden;
    border-radius: 5px;
}

#social-friends::-webkit-scrollbar {
    width: 6px;
}

#social-friends::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

.clan-panel {
    width: 480px;
    text-align: center;
}

.clan-panel .clan-creation {
    padding: 25px;
}

#clan-create {
    display: flex;
    flex-direction: column;
    margin-top: 25px;
    align-items: center;
}

#clan-create input[type="text"] {
    width: 150px;
}

#clan-create * {
    margin: 5px;
}

.back-button {
    position: absolute !important;
}

.back-button i {
    color: white;
    font-size: 38px;
}

#clan-member {
    display: flex;
    flex-direction: column;
}

#clan-member img#social-clan-avatar {
    width: 125px;
    height: 125px;
}

.material-icons#user-badge {
    vertical-align: middle;
    margin-left: 5px;
    cursor: default;
    font-size: 16px;
}

.popup-login-content {
    color: white;
    width: 90%;
    margin: 10px auto;
    text-align: center;
}

.login-input {
    border-style: solid;
    border-width: 1px;
    border-color: #ffffff;
    background-color: rgba(0, 0, 0, .2);
    border-radius: 10px;
    color: #ffffff;
    font-size: 20px;
    height: 30px;
    width: 250px;
    margin-bottom: 5px;
    text-indent: 5px;
}

#btn-login {
    margin-right: 30px;
}

#id-box {
    margin-bottom: 10px;
}

.closebutton_closeButton--3abym {
    pointer-events: all;
}

#cookie-banner {
    display: flex;
    flex-flow: row nowrap;
    position: absolute;
    background-color: whitesmoke;
    bottom: 0;
    left: 0;
    right: 0;
    font-family: Arial, Helvetica, sans-serif;
    font-size: 16px;
    color: darkslategray;
    padding: 15px;
    line-height: 1.5em;
    justify-content: space-between;
    z-index: 10;
}

#cookie-banner .description {
    width: 80%;
}

#cookie-banner .description a {
    pointer-events: all;
}

#cookie-banner .buttons {
    width: 20%;
    min-width: 260px;
    height: 100%;
    margin: auto;
    text-align: center;
}

#cookie-banner .buttons button {
    padding: 7px 15px;
    border: 1px solid darkslategray;
    margin-top: 3px;
    background-color: whitesmoke;
    font-weight: bold;
    font-size: 18px;
    color: darkslategray;
    outline: none;
}

#cookie-banner .buttons #declineCookies:hover {
    background-color: #ff6961;
    border: 1px solid #df4941;
    color: whitesmoke;
}

#cookie-banner .buttons #acceptCookies:hover {
    background-color: #69ff61;
    border: 1px solid #49df41;
}

#touch_controls {
    display: none;
    position: absolute;
    bottom: 20%;
    width: 100vw;
}

#touch_controls #joystick {
    position: absolute;
}

#touch_controls #touch_buttons {
    position: absolute;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    right: 10%;
    bottom: 20%;
    height: 150px;
    width: 150px;
    /* background: green; */
}

#touch_buttons .touch_button {
    display: block;
    width: 75px;
    height: 75px;
    border-radius: 50%;
    background: radial-gradient(transparent, #000000);
    opacity: 0.3;
    color: white;
    border: 1px solid white;
    outline: none;
    font-size: 20px;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    z-index: 10;
    -webkit-tap-highlight-color: transparent;
    pointer-events: all;
}

#context-menu {
    position: absolute;
    z-index: 10;
    border-radius: 5px;
}

#context-name {
    border-bottom: 1px solid;
    border-color: #606060;
    color: #AFAFAF;
    font-weight: 700;
}

.context-list {
    list-style-type: none;
    margin: 0;
    padding: 0;
    float: left;
    border-radius: 5px;
    background-color: rgb(81, 81, 81);
    font-family: "Open Sans";
    font-size: 14px;
    color: #fff;
}

.context-list li {
    padding: 5px;
    pointer-events: auto;
}

.context-action:hover {
    background-color: rgb(128, 128, 128);
}

/* Fix for sub panel chat colors */

.sp-thumb-el {
    pointer-events: all;
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
}

/* Ads */

#preroll {
    width: 960px;
    height: 580px;
    position: absolute;
    z-index: 999999;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    display: none;
}

/* Scrimmage */

#main-scrimmage {
    position: absolute;
    margin: 0px auto;
    top: 15%;
    right: 0;
    bottom: 0;
    left: 0;
    width: 600px;
    height: 400px;
    z-index: 1;
    color: white;
    font-family: "Open Sans";
}

.scrimmage-full {
    width: 100%;
    height: 100%;
}

.scrimmage-content {
    width: 95%;
    height: 75%;
    margin: 10px auto;
}

.scrimmage-right a {
    color: inherit;
}

.scrimmage-right a:hover {
    color: currentColor;
    filter: brightness(150%);
}

.scrimmage-left {
    display: inline-block;
    width: 180px;
    height: 105px;
    margin-right: 5px;
    vertical-align: top;
    font-size: 15px;
}

.scrimmage-left .scrimmage-mode-box {
    margin-bottom: 10px;
    max-height: 300px;
}

.scrimmage-main-left>.gota-btn {
    margin-top: 10px;
    margin-bottom: 5px;
    width: 80px;
}

.scrimmage-main-left {
    font-size: 16px;
}

.scrimmage-right {
    display: inline-block;
    width: 380px;
    height: 300px;
    vertical-align: top;

    overflow-y: auto;
    overflow-x: hidden;
    word-break: break-word;

    font-size: 14px;
}

#scrimmage-options-leader {
    display: block;
    height: 300px;
}

#scrimmage-options-leader .custom-game {
    margin-bottom: 12px;
}

#scrimmage-options-leader::-webkit-scrollbar {
    width: 7px;
}

#scrimmage-options-leader::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

#btn-custom-start,
#btn-lobbies-join,
#btn-lobbies-refresh {
    margin-right: 15px;
}

#scrimmage-startmass {
    width: 90px;
}

#scrimmage-virusDensity,
#scrimmage-respawnDelay {
    width: 90px;
}

#scrimmage-name {
    padding-left: 10px;
    padding-right: 10px;
    pointer-events: auto;
}

#scrimmage-name:hover {
    cursor: pointer;
}

.scrimmage-mode-box {
    padding: 7px 10px;
    box-sizing: border-box;
    border-radius: 10px;
    overflow-y: auto;
}

#scrimmage-mode-info::-webkit-scrollbar {
    width: 7px;
}

#scrimmage-mode-info::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

.scrimmage-select {
    width: 90%;
}

#scrimmage-btn-leave {
    text-align: center;
    padding: 4px 0 2px;
}

#btn-leave-match {
    pointer-events: all;
}

.scrimmage-playerlist-table {
    display: table;
    width: 100%;
    text-align: left;
}

.scrimmage-playerlist-table thead {
    font-size: 16px;
}

#scrimmage-custom-players tr td:nth-child(2) {
    text-align: center;
}

.scrimmage-list-table {
    width: 100%;
}

.scrimmage-list-table thead tr th {
    border-bottom: 1px solid white;
}

#scrimmage-lobbies .scrimmage-content {
    border-radius: 5px;
}

#scrimmage-lobbies-tbody tr td:nth-last-child(-n+4) {
    text-align: center;
}

#scrimmage-lobbies-tbody tr td {
    height: 20px;
    position: relative;
}

#scrimmage-lobbies-tbody tr td:before {
    content: '&nbsp;';
    visibility: hidden;
}

#scrimmage-lobbies-tbody tr td span {
    position: absolute;
    left: 0;
    right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.custom-game-selected {
    background-color: rgba(94, 174, 255, .7);
}

#scrimmage-lobbies-list {
    width: 100%;
}

.scrimmage-lock {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: middle !important;
    margin-right: 5px;
    background-size: cover;
    background-image: url("images/lock.png");
}

/* Etc */

.policyLinks {
    display: flex;
    justify-content: space-around;
    width: 90%;
    margin: 0 auto;
}

.policyLinks a {
    color: inherit;
    text-decoration: none;
}

.policyLinks a:hover {
    font-weight: bolder;
}

#aip_gdpr .switch_switch--YfiyU {
    pointer-events: all;
}

/* .status-container {
    text-align: center;
}
.current-status {
    display: inline-block;
    text-decoration: none;
    color: inherit;
    text-align: center;
    vertical-align: text-bottom;
    width: 100%;
    font-size: 10px;
}

.color-dot {
  -moz-border-radius: 99px;
  -webkit-border-radius: 99px;
  border-radius: 99px;
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 5px;
}
.color-dot.critical {
  background-color: #e74c3c;
}
.color-dot.major {
  background-color: #e67e22;
}
.color-dot.minor {
  background-color: #f1c40f;
}
.color-dot.none {
  background-color: #2ecc71;
} */

.stealth-link {
    font-weight: bold;
    text-decoration: underline;
    color: inherit;
}

#main-discord {
    height: 200px;
}

.discord-login-container {
    padding: 30px;
    border-radius: 0 0 5px 5px;
}

#discordLinkStatus {
    font-size: 12px;
}

.discord-login-button {
    text-decoration: none;
    display: block;
    line-height: 35px;
    padding: 0 12px;
    background: #7289DA;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
    color: #ffffff;
    font-weight: 700;
    font-size: 16px;
}

.discord-login-button:hover {
    background: #697ec4;
}

#main-tokens {
    height: 260px;
    margin-top: 10px;
}

.main-mini-container {
    width: 90%;
    text-align: center;
    margin: 0 auto;
}

#redeem-spend {
    width: 25%;
    height: 20px;
}

#popup-account-username {
    padding-bottom: 5px;
}

#popup-food-colors {
    width: 420px;
}

.food-colors-table {
    table-layout: fixed;
    width: 400px;
    height: 255px;
    margin: 10px 10px;
    padding-bottom: 5px;
}

#food-color-list {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    font-family: "Open Sans";
    font-size: 20pt;
}

#food-color-list::-webkit-scrollbar {
    width: 7px;
}

#food-color-list::-webkit-scrollbar-thumb {
    background-color: darkgray;
    border-radius: 4px;
}

#food-color-list>div {
    display: inline-block;
    width: 95%;
    margin-bottom: 5px;
    border-radius: 10px;
    background: white;
}

#food-color-list span {
    -webkit-user-select: all;
    -moz-user-select: all;
    -ms-user-select: all;
    user-select: all;
    pointer-events: all;
}

#food-color-list>div>div {
    float: right;
    margin-right: 10px;
}

#food-color-list button {
    background: none;
    color: inherit;
    border: none;
    padding: 0;
    font: inherit;
    cursor: pointer;
    outline: inherit;
}

#popup-asset-skinner {
    width: 400px;
}

.asset-skinner-center {
    margin: 10px 0;
}

.asset-skinner-center input {
    width: 90%;
    height: 29px;
    font-size: 20px;
    margin-bottom: 5px;
}

.asset-skinner-center button {
    width: 45%;
}

.asset-skinner-last {
    padding-bottom: 5px;
}

.popup-chat-tab-editor-table {
    table-layout: fixed;
    white-space: nowrap;
    font-size: 12pt;
    font-family: "Open Sans";
    width: 90%;
    margin: 0 auto;
}

.popup-chat-tab-editor-table td:last-child {
    text-align: right;
}

#cte-tab-selector {
    width: 100%;
    text-align-last: center;
    border-radius: 5px;
    height: 20px;
}

#cte-tab-name {
    width: 100%;
    text-align: center;
}

#cte-max-messages {
    width: 90%;
    text-align: center;
}

.popup-chat-tab-editor-last {
    margin: 10px auto;
    padding-bottom: 10px;
}

.popup-chat-tab-editor-last .gota-btn {
    margin: auto 5px;
}

.chat-tab-editor-position {
    height: 20px;
}

#popup-message-content {
    width: 90%;
    font-family: "Open Sans";
    font-size: 12pt;
    margin: 5px auto;
    text-indent: 25px;
}

#popup-message-read-btn {
    width: initial;
}

#popup-message .profile-actions {
    padding-bottom: 10px;
}

.error-banner {
    background-color: #D65050;
    color: white;
    padding: 25px;
    z-index: 99999;
    position: absolute;
    width: 100%;
    font-family: "Open Sans";
    font-size: 14px;
}

/* CSS Loader by Luke Haas */
.loader,
.loader:before,
.loader:after {
    background: #ffffff;
    -webkit-animation: load1 1s infinite ease-in-out;
    animation: load1 1s infinite ease-in-out;
    width: 1em;
    height: 4em;
}

.loader {
    color: #ffffff;
    text-indent: -9999em;
    margin: 88px auto;
    position: relative;
    font-size: 11px;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
}

.loader:before,
.loader:after {
    position: absolute;
    top: 0;
    content: '';
}

.loader:before {
    left: -1.5em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.loader:after {
    left: 1.5em;
}

@-webkit-keyframes load1 {

    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }

    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}

@keyframes load1 {

    0%,
    80%,
    100% {
        box-shadow: 0 0;
        height: 4em;
    }

    40% {
        box-shadow: 0 -2em;
        height: 5em;
    }
}

.spin {
    animation-name: spin;
    animation-duration: 5000ms;
    animation-iteration-count: infinite;
    animation-timing-function: linear;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

[data-balloon],
[data-balloon]:before,
[data-balloon]:after {
    z-index: 9999;
}

.hide-captcha-badge .grecaptcha-badge {
    visibility: hidden;
}

/* ================================== */
/* == CONTEÚDO DO SITESTYLE.CSS == */
/* ================================== */

:root {
    --bg-color: #161616;
    --panel-bg: #1E1E1E;
    --input-bg: #2D2D2D;
    --hover-bg: #3c3c3c;
    --text-primary: #EAEAEA;
    --text-secondary: #9E9E9E;
    --border-color: rgba(255, 255, 255, 0.08);
    --active-server-bg: #282828;
    --anim-speed: 0.5s;
    --anim-curve: cubic-bezier(0.3, 1, 0.3, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body.ui-locked .main-container {
    opacity: 0.6;
    pointer-events: none;
}

.main-container {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    transition: opacity 0.3s ease;
    transform: scale(0.9);
}

.left-column,
.right-column {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.left-column {
    width: 320px;
}

.right-column {
    width: 400px;
}

.top-bar,
.panel-wrapper {
    opacity: 0;
    transform: translateY(20px);
    transition: transform var(--anim-speed) var(--anim-curve), opacity calc(var(--anim-speed) * 0.8) ease;
}

.top-bar.visible,
.panel-wrapper.visible {
    opacity: 1;
    transform: translateY(0);
}

.player-panel {
    background: var(--panel-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    opacity: 0;
    transition: all 0.5s var(--anim-curve);
}

.player-panel.visible {
    max-height: 500px;
    padding: 2.12rem;
    opacity: 1;
}

.top-bar {
    width: 100%;
    height: 56px;
}

.header-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--panel-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 8px;
    width: 100%;
    height: 100%;
}

.left-column .header-bar {
    justify-content: center;
}

.icon-button {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 30px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    transition: all 0.2s ease;
}

.icon-button:hover {
    color: var(--text-primary);
    background-color: var(--input-bg);
}

.icon-button.active {
    background-color: var(--input-bg);
    color: var(--text-primary);
}

.player-panel>* {
    transition: opacity 0.3s ease 0.2s;
}

.player-panel:not(.visible)>* {
    opacity: 0;
}

.player-panel .action-btn {
    margin-bottom: 0;
}

.player-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.panel-wrapper {
    position: relative;
    height: var(--panel-height, auto);
    transition-property: height, opacity, transform;
    transition-duration: 0.4s, var(--anim-speed), var(--anim-speed);
    transition-timing-function: var(--anim-curve), ease, var(--anim-curve);
}

.panel {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 0;
    background: var(--panel-bg);
    border: 1px solid var(--border-color);
    border-radius: 16px;
}

.scroll-container {
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    padding: 1rem;
}

.list-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn,
.region-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-family: inherit;
    font-weight: 600;
    border: 1px solid var(--border-color);
    background: var(--input-bg);
    color: var(--text-primary);
    border-radius: 10px;
    transition: all 0.2s ease;
}

.action-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
}

.action-btn:hover,
.region-btn:hover:not(.switching) {
    border-color: var(--text-secondary);
    background: var(--hover-bg);
}

.player-panel .action-btn:nth-of-type(2) {
    opacity: 0.5;
    pointer-events: none;
}

.region-btn {
    position: relative;
    overflow: hidden;
    z-index: 1;
    height: 40px;
    padding: 0 1.5rem;
    font-size: 0.9rem;
}

.region-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0;
    background-color: rgba(255, 255, 255, 0.1);
    transition: width 1s ease-out;
    z-index: -1;
}

.region-btn.switching::before {
    width: 100%;
}

.name-box {
    flex-grow: 1;
}

.name-box input {
    width: 100%;
    height: 40px;
    background: var(--input-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0 1rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    outline: none;
    transition: border-color 0.2s ease;
}

.name-box input:focus {
    border-color: var(--text-secondary);
}

.server-row {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
    transition: transform 0.5s var(--anim-curve-spring), opacity 0.4s ease, background-color 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    cursor: pointer;
    border-radius: 10px;
}

.server-row:hover:not(.active) {
    background-color: var(--input-bg);
}

.server-row.active {
    background-color: var(--active-server-bg);
}

.server-row.visible {
    opacity: 1;
    transform: scale(1) translateY(0);
}

.server-text,
.server-desc {
    font-size: 1rem;
}

.server-desc {
    color: var(--text-secondary);
}

.server-row.active .server-text,
.server-row.active .server-desc {
    color: var(--text-primary);
}

.skeleton-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--input-bg);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    height: 57px;
}

.skeleton-text {
    height: 20px;
    background-color: var(--hover-bg);
    border-radius: 5px;
}

.skeleton-text.short {
    width: 30%;
}

.skeleton-text.long {
    width: 50%;
}

.skeleton-row::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    animation: shimmer 1.5s infinite;
    transform: translateX(-100%);
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* ================================== */
/* == NOVO BLOCO DE SOBRESCRITA UI == */
/* ================================== */
body {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 100vh !important;
    overflow: hidden !important;
    background-color: var(--bg-color) !important;
    font-family: 'Inter', sans-serif !important;
}

#main {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
}